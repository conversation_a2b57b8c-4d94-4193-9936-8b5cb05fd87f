#!/usr/bin/env python3
"""
Simple script to export the fine-tuned checkpoint to ONNX.
"""

import sys
import os
from pathlib import Path
import torch
import json

# Add piper to Python path
piper_path = Path("finetune/piper/src/python")
sys.path.insert(0, str(piper_path))

def export_checkpoint():
    """Export the checkpoint to ONNX."""
    
    try:
        from piper_train.vits.lightning import VitsModel
        
        # Paths
        checkpoint_path = "/home/<USER>/Documents/personal/test/finetune/training_output/lightning_logs/version_10/checkpoints/epoch=2839-step=4040.ckpt"
        output_dir = Path("/home/<USER>/Documents/personal/test/finetune/finetuned_model")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        model_path = output_dir / "model.onnx"
        config_path = output_dir / "config.json"
        
        print(f"🔄 Loading checkpoint: {checkpoint_path}")
        
        # Load the model
        model = VitsModel.load_from_checkpoint(checkpoint_path, dataset=None)
        model_g = model.model_g
        
        print(f"✅ Model loaded successfully")
        print(f"📊 Vocab size: {model_g.n_vocab}")
        print(f"🎤 Speakers: {model_g.n_speakers}")
        
        # Move model to CPU and set to eval mode
        model_g.cpu()
        model_g.eval()

        print(f"🔄 Exporting to ONNX: {model_path}")

        # Create dummy inputs for ONNX export (on CPU)
        sequence_length = 50
        text = torch.randint(0, model_g.n_vocab, (1, sequence_length), dtype=torch.long)
        text_lengths = torch.tensor([sequence_length], dtype=torch.long)
        scales = torch.tensor([0.667, 1.0, 0.8], dtype=torch.float32)
        
        # Always use multi-speaker format since our model has 18 speakers
        sid = torch.tensor([0], dtype=torch.long)  # Use speaker 0 (our fine-tuned speaker)
        dummy_input = (text, text_lengths, scales, sid)
        input_names = ["input", "input_lengths", "scales", "sid"]
        dynamic_axes = {
            "input": {0: "batch_size", 1: "phonemes"},
            "input_lengths": {0: "batch_size"},
            "output": {0: "batch_size", 2: "time"}
        }
        
        # Export to ONNX
        torch.onnx.export(
            model_g,
            dummy_input,
            str(model_path),
            input_names=input_names,
            output_names=["output"],
            dynamic_axes=dynamic_axes,
            opset_version=15,
            do_constant_folding=True,
        )
        
        print(f"✅ ONNX export completed: {model_path}")
        
        # Copy config from training
        training_config_path = "/home/<USER>/Documents/personal/test/finetune/training_output/config.json"
        if Path(training_config_path).exists():
            import shutil
            shutil.copy2(training_config_path, config_path)
            print(f"✅ Config copied: {config_path}")
        else:
            print(f"⚠️ Training config not found: {training_config_path}")
        
        # Show file sizes
        if model_path.exists():
            size_mb = model_path.stat().st_size / (1024 * 1024)
            print(f"📁 Model size: {size_mb:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model():
    """Test the exported model."""
    
    model_path = Path("finetune/finetuned_model/model.onnx")
    config_path = Path("finetune/finetuned_model/config.json")
    
    if not model_path.exists():
        print(f"❌ Model not found: {model_path}")
        return False
    
    try:
        # Add piper to path for inference
        piper_path = Path("finetune/piper/src/python")
        sys.path.insert(0, str(piper_path))
        
        from piper import PiperVoice
        import wave
        
        print(f"🎤 Testing model with Nepali text...")
        
        # Test texts
        short_text = "नमस्कार"
        long_text = "नमस्कार, यो एक परीक्षण हो। म नेपाली भाषामा बोल्दै छु।"
        
        # Load voice
        voice = PiperVoice.load(str(model_path), config_path=str(config_path))
        
        print(f"✅ Voice loaded successfully")
        print(f"📊 Sample rate: {voice.config.audio.sample_rate} Hz")
        
        # Test short text
        print(f"🔊 Generating audio for: '{short_text}'")
        audio_short = voice.synthesize(short_text)
        
        # Save short audio
        with wave.open("finetune/test_short.wav", 'wb') as wav_file:
            wav_file.setparams((1, 2, voice.config.audio.sample_rate, 0, 'NONE', 'NONE'))
            wav_file.writeframes(audio_short)
        
        print(f"✅ Short audio saved: finetune/test_short.wav")
        
        # Test long text
        print(f"🔊 Generating audio for: '{long_text}'")
        audio_long = voice.synthesize(long_text)
        
        # Save long audio
        with wave.open("finetune/test_long.wav", 'wb') as wav_file:
            wav_file.setparams((1, 2, voice.config.audio.sample_rate, 0, 'NONE', 'NONE'))
            wav_file.writeframes(audio_long)
        
        print(f"✅ Long audio saved: finetune/test_long.wav")
        
        # Show audio info
        short_duration = len(audio_short) / (voice.config.audio.sample_rate * 2)
        long_duration = len(audio_long) / (voice.config.audio.sample_rate * 2)
        
        print(f"📊 Short audio: {short_duration:.2f} seconds")
        print(f"📊 Long audio: {long_duration:.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🚀 Fine-tuned Model Export & Test")
    print("=" * 50)
    
    # Export model
    print("\n🔄 Step 1: Export checkpoint to ONNX")
    export_success = export_checkpoint()
    
    if not export_success:
        print("❌ Export failed. Exiting.")
        sys.exit(1)
    
    # Test model
    print("\n🧪 Step 2: Test exported model")
    test_success = test_model()
    
    if test_success:
        print("\n🎉 SUCCESS!")
        print("✅ Fine-tuned model exported and tested")
        print("📁 Files created:")
        print("   - finetune/finetuned_model/model.onnx")
        print("   - finetune/finetuned_model/config.json")
        print("   - finetune/test_short.wav")
        print("   - finetune/test_long.wav")
        print("\n🎵 Play the audio files to hear your fine-tuned voice!")
    else:
        print("\n⚠️ Export succeeded but test failed")
        print("Check the exported files manually")

if __name__ == "__main__":
    main()
