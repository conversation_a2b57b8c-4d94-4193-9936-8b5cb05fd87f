#!/usr/bin/env python3
"""
Test the converted ONNX model using the same approach as piper_api.
"""

import sys
import wave
from pathlib import Path

# Add piper_api to Python path
sys.path.insert(0, str(Path("piper_api")))

def test_onnx_model():
    """Test the ONNX model with Nepali text."""
    
    model_path = Path("finetune/finetuned_model/model.onnx")
    config_path = Path("finetune/finetuned_model/config.json")
    
    if not model_path.exists() or not config_path.exists():
        print(f"❌ Model files not found!")
        print(f"   Model: {model_path}")
        print(f"   Config: {config_path}")
        return False
    
    try:
        from voice import PiperVoice
        
        print(f"🎤 Testing fine-tuned Nepali TTS model...")
        print(f"📁 Model: {model_path}")
        print(f"📁 Config: {config_path}")
        
        # Test texts in Nepali
        test_texts = [
            "नमस्कार",
            "यो एक परीक्षण हो।",
            "नमस्कार, यो मेरो फाइन-ट्युन गरिएको आवाज हो।",
            "म नेपाली भाषामा बोल्दै छु। यो राम्रो छ।"
        ]
        
        # Load voice
        print(f"🔄 Loading ONNX model...")
        voice = PiperVoice.load(str(model_path), config_path=str(config_path))
        
        print(f"✅ Voice loaded successfully")
        print(f"🎤 Sample rate: {voice.config.sample_rate} Hz")
        print(f"🎭 Number of speakers: {voice.config.num_speakers}")
        
        # Test each text
        for i, text in enumerate(test_texts):
            print(f"\n🔊 Generating audio {i+1}: '{text}'")
            
            # Generate audio using the same method as piper_api
            audio_bytes = b""
            for chunk in voice.synthesize_stream_raw(text, speaker_id=0):
                audio_bytes += chunk
            
            # Save audio file
            output_file = f"test_output_{i+1}.wav"
            with wave.open(output_file, 'wb') as wav_file:
                voice.synthesize(text, wav_file, speaker_id=0)
            
            # Calculate duration
            duration = len(audio_bytes) / (voice.config.sample_rate * 2)
            print(f"✅ Audio saved: {output_file} ({duration:.2f}s)")
        
        print(f"\n📊 Test Summary:")
        print(f"   ✅ All {len(test_texts)} texts synthesized successfully")
        print(f"   🎤 Using speaker ID: 0 (fine-tuned voice)")
        print(f"   📁 Audio files: test_output_1.wav to test_output_{len(test_texts)}.wav")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("🎯 ONNX Model Inference Test")
    print("=" * 50)
    print("🎤 Testing fine-tuned Nepali TTS model")
    print("🔧 Using piper_api voice loading approach")
    print("=" * 50)
    
    success = test_onnx_model()
    
    if success:
        print("\n🎉 SUCCESS!")
        print("✅ ONNX model inference working correctly")
        print("\n🎵 Play the generated audio files to hear your fine-tuned voice!")
        print("💡 The model is ready for deployment in piper_api")
    else:
        print("\n❌ Test failed. Check the error messages above.")

if __name__ == "__main__":
    main()
